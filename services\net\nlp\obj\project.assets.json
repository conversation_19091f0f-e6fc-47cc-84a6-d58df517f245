{"version": 3, "targets": {"net8.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0": ["Microsoft.ML >= 4.0.0", "OpenNLP.NET >= 1.9.4.1", "System.Formats.Asn1 >= 8.0.1", "System.Security.Cryptography.Pkcs >= 8.0.1", "System.Text.Json >= 8.0.5", "TNO.Services >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\TNO.Services.NLP.csproj", "projectName": "TNO.Services.NLP", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\TNO.Services.NLP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\root\\.nuget\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.ML": {"target": "Package", "version": "[4.0.0, )"}, "OpenNLP.NET": {"target": "Package", "version": "[1.9.4.1, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[8.0.1, )"}, "System.Security.Cryptography.Pkcs": {"target": "Package", "version": "[8.0.1, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}, "TNO.Services": {"target": "Package", "version": "[1.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.11, 8.0.11]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.11, 8.0.11]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.11, 8.0.11]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.11, 8.0.11]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "TNO.Services"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "Microsoft.ML"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "System.Text.Json"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "System.Formats.Asn1"}]}