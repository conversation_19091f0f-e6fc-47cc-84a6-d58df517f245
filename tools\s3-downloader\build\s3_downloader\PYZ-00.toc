('C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\build\\s3_downloader\\PYZ-00.pyz',
 [('PySide6',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE'),
  ('PySide6.support',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE'),
  ('PySide6.support.deprecated',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\_compression.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\bisect.py',
   'PYMODULE'),
  ('boto3',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\__init__.py',
   'PYMODULE'),
  ('boto3.compat',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\compat.py',
   'PYMODULE'),
  ('boto3.crt',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\crt.py',
   'PYMODULE'),
  ('boto3.docs',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\__init__.py',
   'PYMODULE'),
  ('boto3.docs.action',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\action.py',
   'PYMODULE'),
  ('boto3.docs.attr',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\attr.py',
   'PYMODULE'),
  ('boto3.docs.base',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\base.py',
   'PYMODULE'),
  ('boto3.docs.client',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\client.py',
   'PYMODULE'),
  ('boto3.docs.collection',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\collection.py',
   'PYMODULE'),
  ('boto3.docs.docstring',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\docstring.py',
   'PYMODULE'),
  ('boto3.docs.method',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\method.py',
   'PYMODULE'),
  ('boto3.docs.resource',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\resource.py',
   'PYMODULE'),
  ('boto3.docs.service',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\service.py',
   'PYMODULE'),
  ('boto3.docs.subresource',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\subresource.py',
   'PYMODULE'),
  ('boto3.docs.utils',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\utils.py',
   'PYMODULE'),
  ('boto3.docs.waiter',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\docs\\waiter.py',
   'PYMODULE'),
  ('boto3.dynamodb',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\dynamodb\\__init__.py',
   'PYMODULE'),
  ('boto3.dynamodb.conditions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\dynamodb\\conditions.py',
   'PYMODULE'),
  ('boto3.dynamodb.table',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\dynamodb\\table.py',
   'PYMODULE'),
  ('boto3.dynamodb.transform',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\dynamodb\\transform.py',
   'PYMODULE'),
  ('boto3.dynamodb.types',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\dynamodb\\types.py',
   'PYMODULE'),
  ('boto3.ec2',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\ec2\\__init__.py',
   'PYMODULE'),
  ('boto3.ec2.createtags',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\ec2\\createtags.py',
   'PYMODULE'),
  ('boto3.ec2.deletetags',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\ec2\\deletetags.py',
   'PYMODULE'),
  ('boto3.exceptions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\exceptions.py',
   'PYMODULE'),
  ('boto3.resources',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\resources\\__init__.py',
   'PYMODULE'),
  ('boto3.resources.action',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\resources\\action.py',
   'PYMODULE'),
  ('boto3.resources.base',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\resources\\base.py',
   'PYMODULE'),
  ('boto3.resources.collection',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\resources\\collection.py',
   'PYMODULE'),
  ('boto3.resources.factory',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\resources\\factory.py',
   'PYMODULE'),
  ('boto3.resources.model',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\resources\\model.py',
   'PYMODULE'),
  ('boto3.resources.params',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\resources\\params.py',
   'PYMODULE'),
  ('boto3.resources.response',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\resources\\response.py',
   'PYMODULE'),
  ('boto3.s3',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\s3\\__init__.py',
   'PYMODULE'),
  ('boto3.s3.constants',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\s3\\constants.py',
   'PYMODULE'),
  ('boto3.s3.inject',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\s3\\inject.py',
   'PYMODULE'),
  ('boto3.s3.transfer',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\s3\\transfer.py',
   'PYMODULE'),
  ('boto3.session',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\session.py',
   'PYMODULE'),
  ('boto3.utils',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\boto3\\utils.py',
   'PYMODULE'),
  ('botocore',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\__init__.py',
   'PYMODULE'),
  ('botocore.args',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\args.py',
   'PYMODULE'),
  ('botocore.auth',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\auth.py',
   'PYMODULE'),
  ('botocore.awsrequest',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\awsrequest.py',
   'PYMODULE'),
  ('botocore.client',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\client.py',
   'PYMODULE'),
  ('botocore.compat',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\compat.py',
   'PYMODULE'),
  ('botocore.compress',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\compress.py',
   'PYMODULE'),
  ('botocore.config',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\config.py',
   'PYMODULE'),
  ('botocore.configloader',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\configloader.py',
   'PYMODULE'),
  ('botocore.configprovider',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\configprovider.py',
   'PYMODULE'),
  ('botocore.credentials',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\credentials.py',
   'PYMODULE'),
  ('botocore.crt',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\crt\\__init__.py',
   'PYMODULE'),
  ('botocore.crt.auth',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\crt\\auth.py',
   'PYMODULE'),
  ('botocore.discovery',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\discovery.py',
   'PYMODULE'),
  ('botocore.docs',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\__init__.py',
   'PYMODULE'),
  ('botocore.docs.bcdoc',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\bcdoc\\__init__.py',
   'PYMODULE'),
  ('botocore.docs.bcdoc.docstringparser',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\bcdoc\\docstringparser.py',
   'PYMODULE'),
  ('botocore.docs.bcdoc.restdoc',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\bcdoc\\restdoc.py',
   'PYMODULE'),
  ('botocore.docs.bcdoc.style',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\bcdoc\\style.py',
   'PYMODULE'),
  ('botocore.docs.client',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\client.py',
   'PYMODULE'),
  ('botocore.docs.docstring',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\docstring.py',
   'PYMODULE'),
  ('botocore.docs.example',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\example.py',
   'PYMODULE'),
  ('botocore.docs.method',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\method.py',
   'PYMODULE'),
  ('botocore.docs.paginator',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\paginator.py',
   'PYMODULE'),
  ('botocore.docs.params',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\params.py',
   'PYMODULE'),
  ('botocore.docs.service',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\service.py',
   'PYMODULE'),
  ('botocore.docs.shape',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\shape.py',
   'PYMODULE'),
  ('botocore.docs.sharedexample',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\sharedexample.py',
   'PYMODULE'),
  ('botocore.docs.utils',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\utils.py',
   'PYMODULE'),
  ('botocore.docs.waiter',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\docs\\waiter.py',
   'PYMODULE'),
  ('botocore.endpoint',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\endpoint.py',
   'PYMODULE'),
  ('botocore.endpoint_provider',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\endpoint_provider.py',
   'PYMODULE'),
  ('botocore.errorfactory',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\errorfactory.py',
   'PYMODULE'),
  ('botocore.eventstream',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\eventstream.py',
   'PYMODULE'),
  ('botocore.exceptions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\exceptions.py',
   'PYMODULE'),
  ('botocore.handlers',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\handlers.py',
   'PYMODULE'),
  ('botocore.history',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\history.py',
   'PYMODULE'),
  ('botocore.hooks',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\hooks.py',
   'PYMODULE'),
  ('botocore.httpchecksum',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\httpchecksum.py',
   'PYMODULE'),
  ('botocore.httpsession',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\httpsession.py',
   'PYMODULE'),
  ('botocore.loaders',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\loaders.py',
   'PYMODULE'),
  ('botocore.model',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\model.py',
   'PYMODULE'),
  ('botocore.monitoring',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\monitoring.py',
   'PYMODULE'),
  ('botocore.paginate',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\paginate.py',
   'PYMODULE'),
  ('botocore.parsers',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\parsers.py',
   'PYMODULE'),
  ('botocore.regions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\regions.py',
   'PYMODULE'),
  ('botocore.response',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\response.py',
   'PYMODULE'),
  ('botocore.retries',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\retries\\__init__.py',
   'PYMODULE'),
  ('botocore.retries.adaptive',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\retries\\adaptive.py',
   'PYMODULE'),
  ('botocore.retries.base',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\retries\\base.py',
   'PYMODULE'),
  ('botocore.retries.bucket',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\retries\\bucket.py',
   'PYMODULE'),
  ('botocore.retries.quota',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\retries\\quota.py',
   'PYMODULE'),
  ('botocore.retries.special',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\retries\\special.py',
   'PYMODULE'),
  ('botocore.retries.standard',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\retries\\standard.py',
   'PYMODULE'),
  ('botocore.retries.throttling',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\retries\\throttling.py',
   'PYMODULE'),
  ('botocore.retryhandler',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\retryhandler.py',
   'PYMODULE'),
  ('botocore.serialize',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\serialize.py',
   'PYMODULE'),
  ('botocore.session',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\session.py',
   'PYMODULE'),
  ('botocore.signers',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\signers.py',
   'PYMODULE'),
  ('botocore.tokens',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\tokens.py',
   'PYMODULE'),
  ('botocore.translate',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\translate.py',
   'PYMODULE'),
  ('botocore.useragent',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\useragent.py',
   'PYMODULE'),
  ('botocore.utils',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\utils.py',
   'PYMODULE'),
  ('botocore.validate',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\validate.py',
   'PYMODULE'),
  ('botocore.vendored',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\vendored\\__init__.py',
   'PYMODULE'),
  ('botocore.vendored.requests',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\vendored\\requests\\__init__.py',
   'PYMODULE'),
  ('botocore.vendored.requests.exceptions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\vendored\\requests\\exceptions.py',
   'PYMODULE'),
  ('botocore.vendored.requests.packages',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\vendored\\requests\\packages\\__init__.py',
   'PYMODULE'),
  ('botocore.vendored.requests.packages.urllib3',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\vendored\\requests\\packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('botocore.vendored.requests.packages.urllib3.exceptions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\vendored\\requests\\packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('botocore.vendored.six',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\vendored\\six.py',
   'PYMODULE'),
  ('botocore.waiter',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\botocore\\waiter.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\copy.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\decimal.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\dis.py',
   'PYMODULE'),
  ('dotenv',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\gettext.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jmespath',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\jmespath\\__init__.py',
   'PYMODULE'),
  ('jmespath.ast',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\jmespath\\ast.py',
   'PYMODULE'),
  ('jmespath.compat',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\jmespath\\compat.py',
   'PYMODULE'),
  ('jmespath.exceptions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\jmespath\\exceptions.py',
   'PYMODULE'),
  ('jmespath.functions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\jmespath\\functions.py',
   'PYMODULE'),
  ('jmespath.lexer',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\jmespath\\lexer.py',
   'PYMODULE'),
  ('jmespath.parser',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\jmespath\\parser.py',
   'PYMODULE'),
  ('jmespath.visitor',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\jmespath\\visitor.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\opcode.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\pathlib.py',
   'PYMODULE'),
  ('peewee',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\peewee.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\py_compile.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\random.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\runpy.py',
   'PYMODULE'),
  ('s3transfer',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\__init__.py',
   'PYMODULE'),
  ('s3transfer.bandwidth',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\bandwidth.py',
   'PYMODULE'),
  ('s3transfer.compat',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\compat.py',
   'PYMODULE'),
  ('s3transfer.constants',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\constants.py',
   'PYMODULE'),
  ('s3transfer.copies',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\copies.py',
   'PYMODULE'),
  ('s3transfer.crt',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\crt.py',
   'PYMODULE'),
  ('s3transfer.delete',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\delete.py',
   'PYMODULE'),
  ('s3transfer.download',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\download.py',
   'PYMODULE'),
  ('s3transfer.exceptions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\exceptions.py',
   'PYMODULE'),
  ('s3transfer.futures',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\futures.py',
   'PYMODULE'),
  ('s3transfer.manager',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\manager.py',
   'PYMODULE'),
  ('s3transfer.subscribers',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\subscribers.py',
   'PYMODULE'),
  ('s3transfer.tasks',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\tasks.py',
   'PYMODULE'),
  ('s3transfer.upload',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\upload.py',
   'PYMODULE'),
  ('s3transfer.utils',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\s3transfer\\utils.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\selectors.py',
   'PYMODULE'),
  ('shiboken6',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\signal.py',
   'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\socket.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('src',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\__init__.py',
   'PYMODULE'),
  ('src.UI',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\__init__.py',
   'PYMODULE'),
  ('src.UI.app',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\app.py',
   'PYMODULE'),
  ('src.UI.components',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\components\\__init__.py',
   'PYMODULE'),
  ('src.UI.components.buttons_panel',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\components\\buttons_panel.py',
   'PYMODULE'),
  ('src.UI.components.file_details_dialog',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\components\\file_details_dialog.py',
   'PYMODULE'),
  ('src.UI.components.history_widget',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\components\\history_widget.py',
   'PYMODULE'),
  ('src.UI.components.log_widget',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\components\\log_widget.py',
   'PYMODULE'),
  ('src.UI.components.numeric_table_widget_item',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\components\\numeric_table_widget_item.py',
   'PYMODULE'),
  ('src.UI.components.schedule_info_widget',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\components\\schedule_info_widget.py',
   'PYMODULE'),
  ('src.UI.components.storage_info_widget',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\components\\storage_info_widget.py',
   'PYMODULE'),
  ('src.UI.controllers',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\controllers\\__init__.py',
   'PYMODULE'),
  ('src.UI.controllers.download_controller',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\controllers\\download_controller.py',
   'PYMODULE'),
  ('src.UI.controllers.scheduler',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\controllers\\scheduler.py',
   'PYMODULE'),
  ('src.UI.controllers.settings_controller',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\controllers\\settings_controller.py',
   'PYMODULE'),
  ('src.UI.main_window',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\main_window.py',
   'PYMODULE'),
  ('src.UI.s3_controller',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\UI\\s3_controller.py',
   'PYMODULE'),
  ('src.client',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\client\\__init__.py',
   'PYMODULE'),
  ('src.client.s3_client',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\client\\s3_client.py',
   'PYMODULE'),
  ('src.database',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\database\\__init__.py',
   'PYMODULE'),
  ('src.database.models',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\database\\models.py',
   'PYMODULE'),
  ('src.settings',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\settings\\__init__.py',
   'PYMODULE'),
  ('src.settings.default_settings',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\settings\\default_settings.py',
   'PYMODULE'),
  ('src.settings.settings_loader',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\src\\settings\\settings_loader.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\Documents\\GitHub\\tno\\tools\\s3-downloader\\.venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\uuid.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.12-windows-x86_64-none\\Lib\\zipimport.py',
   'PYMODULE')])
