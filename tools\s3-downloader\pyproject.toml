[project]
name = "s3-downloader"
version = "0.1.0"
description = "S3 downloader tool for downloading files from S3 bucket"
readme = "README.md"
requires-python = ">=3.11, <3.12"
dependencies = [
    "boto3>=1.28.0",
    "dotenv>=0.9.9",
    "python-dotenv>=1.0.0",
    "ruff>=0.11.8",
    "PySide6>=6.5.0",
    "peewee>=3.16.0",
    "pyright>=1.1.400",
    "peewee-migrate>=1.13.0",
    "pyinstaller>=6.13.0",
]

[tool.ruff]
line-length = 100
target-version = "py311"

# lint
[tool.ruff.lint]

select = [
    "E",  # pycodestyle errors
    "F",  # pyflakes
    "I",  # isort
    "W",  # pycodestyle warnings
]

ignore = [
    "E501",  # line too long
]

# format
[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"

# isort
[tool.ruff.lint.isort]
known-first-party = ["src"]
