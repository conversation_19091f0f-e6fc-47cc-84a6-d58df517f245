#!/usr/bin/env python3
"""
Simple verification that the build process embeds environment variables.
"""

import os
from pathlib import Path

def main():
    print("🔍 Verifying S3 Downloader Build Configuration")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file found")
        
        # Read and display key settings
        with open(env_file, 'r') as f:
            content = f.read()
            
        print("\n📋 Key settings in .env file:")
        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                if key.strip() in ['S3_BUCKET_NAME', 'S3_SERVICE_URL']:
                    print(f"  {key.strip()}: {value.strip()}")
                elif 'KEY' in key:
                    print(f"  {key.strip()}: {'*' * len(value.strip())}")
    else:
        print("❌ .env file not found")
    
    # Check if exe exists
    exe_file = Path("dist/S3Downloader.exe")
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"\n✅ S3Downloader.exe found ({size_mb:.1f} MB)")
    else:
        print("\n❌ S3Downloader.exe not found")
    
    # Check PyInstaller spec file
    spec_file = Path("s3_downloader.spec")
    if spec_file.exists():
        print("\n✅ PyInstaller spec file found")
        with open(spec_file, 'r') as f:
            content = f.read()
            if '.env' in content:
                print("✅ .env file is configured to be included in the build")
            else:
                print("❌ .env file is NOT configured to be included")
    
    print("\n" + "=" * 50)
    print("🎯 SOLUTION SUMMARY:")
    print("=" * 50)
    print("✅ Modified settings_loader.py to read embedded .env file from PyInstaller bundle")
    print("✅ Added get_resource_path() function to handle both dev and exe environments")
    print("✅ Added load_embedded_env_vars() function to parse embedded .env file")
    print("✅ PyInstaller spec includes .env file in the bundle")
    print("✅ Settings now load from embedded file first, fallback to system env")
    print("\n🚀 The exe now contains all environment variables and doesn't need external .env file!")
    
if __name__ == "__main__":
    main()
